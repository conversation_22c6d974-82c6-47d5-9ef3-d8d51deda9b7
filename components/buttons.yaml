binary_sensor:
  - platform: gpio
    id: center_button
    internal: true
    pin:
      number: GPIO47
      mode: INPUT_PULLUP
      inverted: true
    on_press:
      - script.execute: control_leds
    on_release:
      - script.execute: control_leds
    on_multi_click:
      # Simple Click:
      #   - Abort "things" in order
      #     - Announcements
      #     - Voice Assistant Pipeline run
      #     - Music
      #   - Starts the voice assistant if it is not yet running and if the device is not muted.
      - timing:
          - ON for at most 1s
          - OFF for at least 0.25s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - if:
                    condition:
                      voice_assistant.is_running:
                    then:
                      - voice_assistant.stop:
                    else:
                      - if:
                          condition:
                            media_player.is_announcing:
                          then:
                            media_player.stop:
                              announcement: true
                          else:
                            - if:
                                condition:
                                  media_player.is_playing:
                                then:
                                  - media_player.pause:
                                else:
                                  - if:
                                      condition:
                                        - not: voice_assistant.is_running
                                      then:
                                        - script.execute:
                                            id: play_sound
                                            priority: true
                                            sound_file: !lambda return id(center_button_press_sound);
                                        - delay: 300ms
                                        - voice_assistant.start:

      # Double Click
      #  . Exposed as an event entity. To be used in automations inside Home Assistant
      - timing:
          - ON for at most 1s
          - OFF for at most 0.25s
          - ON for at most 1s
          - OFF for at least 0.25s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - script.execute:
                    id: play_sound
                    priority: false
                    sound_file: !lambda return id(center_button_double_press_sound);
                - event.trigger:
                    id: button_press_event
                    event_type: 'double_press'

      # Triple Click
      #  . Exposed as an event entity. To be used in automations inside Home Assistant
      - timing:
          - ON for at most 1s
          - OFF for at most 0.25s
          - ON for at most 1s
          - OFF for at most 0.25s
          - ON for at most 1s
          - OFF for at least 0.25s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - script.execute:
                    id: play_sound
                    priority: false
                    sound_file: !lambda return id(center_button_triple_press_sound);
                - event.trigger:
                    id: button_press_event
                    event_type: 'triple_press'

      # Long Press
      #  . Exposed as an event entity. To be used in automations inside Home Assistant
      - timing:
          - ON for at least 1s
        then:
          - if:
              condition:
                lambda: return !id(init_in_progress) && !id(color_changed);
              then:
                - script.execute:
                    id: play_sound
                    priority: false
                    sound_file: !lambda return id(center_button_long_press_sound);
                - light.turn_off: voice_assistant_leds
                - event.trigger:
                    id: button_press_event
                    event_type: 'long_press'

event:
  - platform: template
    id: button_press_event
    name: 'Button Press'
    icon: mdi:button-pointer
    device_class: button
    event_types:
      - double_press
      - triple_press
      - long_press
