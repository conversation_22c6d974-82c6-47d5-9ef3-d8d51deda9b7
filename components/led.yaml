globals:
  - id: global_led_animation_index
    type: int
    restore_value: no
    initial_value: '0'

  - id: color_changed
    type: bool
    restore_value: no
    initial_value: 'false'

light:
  - platform: esp32_rmt_led_strip
    id: leds_internal
    internal: true
    pin: GPIO38
    chipset: WS2812
    num_leds: 12
    max_refresh_rate: 15ms
    rgb_order: GRB
    rmt_symbols: 192
    default_transition_length: 0ms

  - platform: partition
    id: led_ring
    name: 'LED Ring'
    entity_category: config
    icon: 'mdi:circle-outline'
    default_transition_length: 0ms
    restore_mode: RESTORE_DEFAULT_OFF
    initial_state:
      color_mode: rgb
      brightness: 66%
      red: 9.4%
      green: 73.3%
      blue: 94.9%
    segments:
      - id: leds_internal
        from: 0
        to: 6
      - id: leds_internal
        from: 7
        to: 11

  - platform: partition
    id: voice_assistant_leds
    internal: true
    default_transition_length: 0ms
    segments:
      - id: leds_internal
        from: 0
        to: 6
      - id: leds_internal
        from: 7
        to: 11
    effects:
      - addressable_twinkle:
          name: 'Twinkle'
          twinkle_probability: 50%

      - addressable_lambda:
          name: 'Waiting for Command'
          update_interval: 100ms
          lambda: |-
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 11) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 10) % 12) {
                it[i] = color * 128;
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 5) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 4) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }

            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;

      - addressable_lambda:
          name: 'Listening For Command'
          update_interval: 50ms
          lambda: |-
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 11) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 10) % 12) {
                it[i] = color * 128;
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 5) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 4) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }

            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;

      - addressable_lambda:
          name: 'Thinking'
          update_interval: 10ms
          lambda: |-
            static uint8_t brightness_step = 0;
            static bool brightness_decreasing = true;
            static uint8_t brightness_step_number = 10;

            if (initial_run) {
              brightness_step = 0;
              brightness_decreasing = true;
            }

            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
              } else {
                it[i] = Color::BLACK;
              }
            }

            if (brightness_decreasing) {
              brightness_step++;
            } else {
              brightness_step--;
            }

            if (brightness_step == 0 || brightness_step == brightness_step_number) {
              brightness_decreasing = !brightness_decreasing;
            }

      - addressable_lambda:
          name: 'Replying'
          update_interval: 50ms
          lambda: |-
            id(global_led_animation_index) = (12 + id(global_led_animation_index) - 1) % 12;
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              if (i == (id(global_led_animation_index)) % 12) {
                it[i] = color;
              } else if (i == ( id(global_led_animation_index) + 1) % 12) {
                it[i] = color * 192;
              } else if (i == ( id(global_led_animation_index) + 2) % 12) {
                it[i] = color * 128;
              } else if (i == ( id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == ( id(global_led_animation_index) + 7) % 12) {
                it[i] = color * 192;
              } else if (i == ( id(global_led_animation_index) + 8) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }

      - addressable_lambda:
          name: 'Error'
          update_interval: 10ms
          lambda: |-
            static uint8_t brightness_step = 0;
            static bool brightness_decreasing = true;
            static uint8_t brightness_step_number = 10;

            if (initial_run) {
              brightness_step = 0;
              brightness_decreasing = true;
            }

            Color error_color(255, 0, 0);

            for (uint8_t i = 0; i < 12; i++) {
              it[i] = error_color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
            }

            if (brightness_decreasing) {
              brightness_step++;
            } else {
              brightness_step--;
            }

            if (brightness_step == 0 || brightness_step == brightness_step_number) {
              brightness_decreasing = !brightness_decreasing;
            }

      - addressable_lambda:
          name: 'Center Button Touched'
          update_interval: 16ms
          lambda: |-
            if (initial_run) {
              auto led_ring_cv = id(led_ring).current_values;
              auto va_leds_call = id(voice_assistant_leds).make_call();

              va_leds_call.from_light_color_values(led_ring_cv);
              va_leds_call.set_brightness( min ( max( id(led_ring).current_values.get_brightness() , 0.2f ) + 0.1f , 1.0f ) );
              va_leds_call.set_state(true);
              va_leds_call.perform();
            }

            auto light_color = id(voice_assistant_leds).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255, light_color.get_blue() * 255);

            for (uint8_t i = 0; i < 12; i++) {
              it[i] = color;
            }

script:
  - id: control_leds
    then:
      - lambda: |
          if (id(improv_ble_in_progress)) {
            id(control_leds_improv_ble_state).execute();
          } else if (id(init_in_progress)) {
            id(control_leds_init_state).execute();
          } else if (!id(wifi_id).is_connected() || !id(api_id).is_connected()){
            id(control_leds_no_ha_connection_state).execute();
          } else if (id(center_button).state) {
            id(control_leds_center_button_touched).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_waiting_for_command_phase_id}) {
            id(control_leds_voice_assistant_waiting_for_command_phase).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_listening_for_command_phase_id}) {
            id(control_leds_voice_assistant_listening_for_command_phase).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_thinking_phase_id}) {
            id(control_leds_voice_assistant_thinking_phase).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_replying_phase_id}) {
            id(control_leds_voice_assistant_replying_phase).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_error_phase_id}) {
            id(control_leds_voice_assistant_error_phase).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_not_ready_phase_id}) {
            id(control_leds_voice_assistant_not_ready_phase).execute();
          } else if (id(voice_assistant_phase) == ${voice_assist_idle_phase_id}) {
            id(control_leds_voice_assistant_idle_phase).execute();
          }

  - id: control_leds_improv_ble_state
    then:
      - light.turn_on:
          brightness: 66%
          red: 100%
          green: 89%
          blue: 71%
          id: voice_assistant_leds
          effect: 'Twinkle'

  - id: control_leds_init_state
    then:
      - if:
          condition:
            wifi.connected:
          then:
            - light.turn_on:
                brightness: 66%
                red: 9.4%
                green: 73.3%
                blue: 94.9%
                id: voice_assistant_leds
                effect: 'Twinkle'
          else:
            - light.turn_on:
                brightness: 66%
                red: 100%
                green: 89%
                blue: 71%
                id: voice_assistant_leds
                effect: 'none'

  - id: control_leds_no_ha_connection_state
    then:
      - light.turn_on:
          brightness: 66%
          red: 1
          green: 0
          blue: 0
          id: voice_assistant_leds
          effect: 'Twinkle'

  - id: control_leds_voice_assistant_idle_phase
    then:
      - light.turn_off: voice_assistant_leds
      - if:
          condition:
            light.is_on: led_ring
          then:
            light.turn_on: led_ring

  - id: control_leds_voice_assistant_waiting_for_command_phase
    then:
      - light.turn_on:
          brightness: !lambda return max( id(led_ring).current_values.get_brightness() , 0.2f );
          id: voice_assistant_leds
          effect: 'Waiting for Command'

  - id: control_leds_voice_assistant_listening_for_command_phase
    then:
      - light.turn_on:
          brightness: !lambda return max( id(led_ring).current_values.get_brightness() , 0.2f );
          id: voice_assistant_leds
          effect: 'Listening For Command'

  - id: control_leds_voice_assistant_thinking_phase
    then:
      - light.turn_on:
          brightness: !lambda return max( id(led_ring).current_values.get_brightness() , 0.2f );
          id: voice_assistant_leds
          effect: 'Thinking'

  - id: control_leds_voice_assistant_replying_phase
    then:
      - light.turn_on:
          brightness: !lambda return max( id(led_ring).current_values.get_brightness() , 0.2f );
          id: voice_assistant_leds
          effect: 'Replying'

  - id: control_leds_voice_assistant_error_phase
    then:
      - light.turn_on:
          brightness: !lambda return min ( max( id(led_ring).current_values.get_brightness() , 0.2f ) + 0.1f , 1.0f );
          red: 1
          green: 0
          blue: 0
          id: voice_assistant_leds
          effect: 'Error'

  - id: control_leds_voice_assistant_not_ready_phase
    then:
      - light.turn_on:
          brightness: 66%
          red: 1
          green: 0
          blue: 0
          id: voice_assistant_leds
          effect: 'Twinkle'

  - id: control_leds_center_button_touched
    then:
      - light.turn_on:
          brightness: !lambda return min ( max( id(led_ring).current_values.get_brightness() , 0.2f ) + 0.1f , 1.0f );
          id: voice_assistant_leds
          effect: 'Center Button Touched'
