i2s_audio:
  - id: i2s_output
    i2s_lrclk_pin: GPIO14
    i2s_bclk_pin: GPIO12

speaker:
  - platform: i2s_audio
    id: i2s_speaker
    i2s_audio_id: i2s_out
    i2s_dout_pin: GPIO13
    dac_type: external
    channel: mono
    sample_rate: 48000
    bits_per_sample: 32bit
    buffer_duration: 100ms
    timeout: never

media_player:
  - platform: speaker
    id: external_media_player
    name: 'Media Player'
    internal: false
    announcement_pipeline:
      speaker: i2s_speaker
      format: FLAC
      num_channels: 1
      sample_rate: 48000
    files:
      - id: wake_word_triggered
        file: 'sounds/wake_word_triggered.flac'

script:
  - id: play_sound
    parameters:
      priority: bool
      sound_file: 'audio::AudioFile*'
    then:
      - lambda: |-
          if (priority) {
            id(external_media_player)->make_call().set_command(media_player::MediaPlayerCommand::MEDIA_PLAYER_COMMAND_STOP).set_announcement(true).perform();
          }

          if ( (id(external_media_player).state != media_player::MediaPlayerState::MEDIA_PLAYER_STATE_ANNOUNCING ) || priority) {
            id(external_media_player)->play_file(sound_file, true, false);
          }
