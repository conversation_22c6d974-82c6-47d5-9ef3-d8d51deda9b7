substitutions:
  voice_assist_idle_phase_id: '1'
  voice_assist_waiting_for_command_phase_id: '2'
  voice_assist_listening_for_command_phase_id: '3'
  voice_assist_thinking_phase_id: '4'
  voice_assist_replying_phase_id: '5'
  voice_assist_not_ready_phase_id: '10'
  voice_assist_error_phase_id: '11'

globals:
  - id: voice_assistant_phase
    type: int
    restore_value: no
    initial_value: ${voice_assist_not_ready_phase_id}

script:
  - id: activate_stop_word_once
    then:
      - delay: 1s
      - micro_wake_word.enable_model: stop
      - wait_until:
          not:
            media_player.is_announcing:
      - micro_wake_word.disable_model: stop
