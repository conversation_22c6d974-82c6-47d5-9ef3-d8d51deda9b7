wifi:
  id: wifi_id
  ap:
  ssid: 'DiepPK'
  password: '26d3i1999'
  on_connect:
    - lambda: id(improv_ble_in_progress) = false;
    - script.execute: control_leds
  on_disconnect:
    - script.execute: control_leds

captive_portal:

sensor:
  - platform: wifi_signal
    id: rssi
    name: 'RSSI'
    entity_category: diagnostic
    icon: 'mdi:wifi'
    update_interval: 5s

text_sensor:
  - platform: wifi_info
    ip_address:
      id: ip_address
      name: 'ESP IP Address'
      entity_category: diagnostic
      icon: 'mdi:ip-network'
    mac_address:
      id: mac_address
      name: 'ESP MAC Address'
      entity_category: diagnostic
      icon: 'mdi:network'
